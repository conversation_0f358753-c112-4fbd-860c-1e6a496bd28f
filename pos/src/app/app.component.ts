import { Component, OnInit, inject } from '@angular/core';
import { IonApp, IonMenu, IonRouterOutlet, MenuController } from '@ionic/angular/standalone';
import { ToastModule } from 'primeng/toast';
import { MenuModule } from 'primeng/menu';
import { Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { filter, takeUntil } from 'rxjs/operators';
import { auth } from './firebase/firebase.config';
import { FirebaseAuthService } from './services/firebase-auth.service';
import { StorageService } from './services/storage';
import { Subject } from 'rxjs';
@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  imports: [
    IonApp,
    IonMenu,
    IonRouterOutlet,
    ToastModule,
    MenuModule,
    CommonModule
  ],
})
export class AppComponent implements OnInit {
  items: any[] = [
    { label: 'Billing', icon: 'fi fi-rr-shopping-cart', command: () => this.onMenuClick('/home') },
    { label: 'Invoices', icon: 'fi fi-rr-file-invoice-dollar', command: () => this.onMenuClick('/invoices')},
    { label: 'Logout', icon: 'fi fi-rr-sign-out-alt', command: () => this.logout() },
  ];

  showMenu = true;
  isAuthenticated = false;
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router, 
    private menuController: MenuController,
    private authService: FirebaseAuthService,
    private storageService: StorageService
  ) {
    // Monitor route changes to show/hide menu
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntil(this.destroy$)
    ).subscribe((event: any) => {
      this.showMenu = !event.url.includes('/login');
    });
    console.log(auth)
    // Monitor authentication state
    this.authService.authState$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.isAuthenticated = !!user || !!this.storageService.getToken();
    });
  }
  
  ngOnInit() {
    // Firebase is automatically initialized when imported
    console.log('Firebase initialized:', auth ? 'Success' : 'Failed');
    
    // Check if user is authenticated on app init
    const token = this.storageService.getToken();
    const user = this.storageService.getItem('user');
    
    if (token && user) {
      console.log('User authenticated via stored credentials');
    }
  }
  onMenuClick(url:string){
    this.router.navigateByUrl(url);
    this.menuController.close();
  }
  logout(){
    this.menuController.close();
    this.authService.signOut();
  }
  
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
