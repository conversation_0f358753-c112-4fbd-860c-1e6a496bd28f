<p-table #table showGridlines stripedRows [paginator]="pagination"   [rows]="10" [rowsPerPageOptions]="[10, 20, 50, 100]" [value]="tableData"
  [dataKey]="dataKey" [scrollable]="scrollable" [scrollHeight]="scrollHeight" [virtualScroll]="false">
  <ng-template #header>
    <tr>
      <th *ngFor="let col of tableColumns" style="width: 6rem">
        <div class="flex items-center">
          {{col.header}}
        </div>
      </th>
    </tr>
  </ng-template>
  <ng-template #body let-product let-rowIndex="rowIndex" let-columns="columns">
    <tr class="cursor-pointer transition-colors">
      <ng-container *ngFor="let col of tableColumns">
        <td *ngIf="col.type === 'image'">
          <div class="flex h-full">
            <ng-container >
              <img [src]="product[col.field]" [alt]="product[col.field]" class="w-10 h-10 rounded object-cover"
                onerror="this.src='https://www.shorekids.co.nz/wp-content/uploads/2014/08/image-placeholder.jpg';" />
            </ng-container>
          </div>
        </td>
        <td *ngIf="col.type === 'quantity'">
          <div class="flex h-full">
            <ng-container>
              <p-inputnumber [(ngModel)]="product[col.field]" [showButtons]="true" buttonLayout="horizontal"
                spinnerMode="horizontal" [min]="1" [max]="999"
                [inputStyle]="{ width: '3rem', height: '1.8rem', textAlign: 'center', fontSize: '0.75rem' }"
                (onInput)="onInputChange($event, product, col)">
                <ng-template #incrementbuttonicon>
                  <span class="pi pi-plus text-xs"></span>
                </ng-template>
                <ng-template #decrementbuttonicon>
                  <span class="pi pi-minus text-xs"></span>
                </ng-template>
              </p-inputnumber>
            </ng-container>
          </div>
        </td>
        <td *ngIf="col.type === 'action'" style="width: 6rem" class="leading-none">
          <div class="flex h-full">
            <ng-container >
              <ng-container *ngFor="let button of col.buttons">
                <button pButton [icon]="button.icon" [class]="button.class" [severity]="button.severity" [outlined]="button.outlined" [label]="button.label" *ngIf="!button.hide" (click)="buttonClick(button, product)"></button>
              </ng-container>
            </ng-container>
          </div>
        </td>
        <td *ngIf="!col.type" (click)="rowClick($event, product, col)">
          <div class="flex h-full">
            <span>{{col.body ? col.body(product) : product[col.field]}}</span>
          </div>
        </td>
      </ng-container>
      
    </tr>
  </ng-template>

  <ng-template #emptymessage>
    <tr>
      <td colspan="100%">
        <div class="p-10">
          <app-no-data></app-no-data>
        </div>
      </td>
    </tr>
  </ng-template>
</p-table>