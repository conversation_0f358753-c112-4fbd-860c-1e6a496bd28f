import { Component } from "@angular/core";
import { IonHeader, IonToolbar, IonButtons, IonTitle, IonMenuButton } from "@ionic/angular/standalone";
import { DropdownModule } from "primeng/dropdown";
import { Facility } from "src/app/models";
import { FormsModule } from "@angular/forms";
import { CommonService } from "src/app/services/common";
@Component({
    selector: 'app-header',
    standalone: true,
    template: `
     <ion-header [translucent]="true">
        <ion-toolbar>
          <div class="flex items-center">
            <ion-buttons slot="start">
              <ion-menu-button autoHide="false"></ion-menu-button>
            </ion-buttons>
            <div class="flex items-center w-full justify-between pr-4">
              <span class="mr-2 flex items-center"><img src="https://djpw4cfh60y52.cloudfront.net/uploads/logo/ljD8TdmW3xUCdu1eHw8Gd31UIsMrYpt6DVHm3kRy.jpg" alt="Logo" width="120px"></span>
              <p-dropdown appendTo="body" [options]="facilities" [(ngModel)]="facility" optionLabel="facilityName" placeholder="Select Facility"></p-dropdown>
            </div>
          </div>
        </ion-toolbar>

    </ion-header>
    `,
    imports: [ IonButtons, IonToolbar, IonHeader, IonMenuButton, DropdownModule, FormsModule]
})
export class HeaderComponent {
  facilities : Facility[] = [
    {
      facilityId: '5883',
      facilityName: 'ROHTAK SORTING HUB',
      facilityCode: 'ROZANA_TEST_WH1'
    }
  ];
  facility: Facility = this.facilities[0];
  constructor(private commonService: CommonService) {
    this.commonService.currentFacility = this.facility;
  }
}