import { Routes } from '@angular/router';
import { authGuard } from './guards/auth.guard';
import { nonAuthGuard } from './guards/non-auth.guard';
import { roleGuard } from './guards/role.guard';

export const routes: Routes = [
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.page').then((m) => m.LoginPage),
    canActivate: [nonAuthGuard]
  },
  {
    path: 'home',
    loadComponent: () => import('./pages/home/<USER>').then((m) => m.HomePage),
    canActivate: [authGuard]
  },
  {
    path: 'invoices',
    loadComponent: () => import('./pages/invoices/invoices').then((m) => m.InvoicesComponent),
    canActivate: [authGuard]
  },
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
];
