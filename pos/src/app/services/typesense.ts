import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";
import { Client } from 'typesense';
import { Product, ProductSearchResult } from '../models';
import { CommonService } from './common';
@Injectable({
  providedIn: 'root'
})
export class TypeSenseService {
  client: Client | any;
  storeId: string = '';

  constructor(
    private commonService: CommonService
  ) {
    this.storeId = this.commonService.currentFacility?.facilityId || '5883';
    this.initiTypesense();
  }

  initiTypesense() {
    this.client = new Client({
      nodes: [{
        host: environment.typesense.host,
        port: Number(environment.typesense.port),
        protocol: environment.typesense.protocol,
      }],
      apiKey: environment.typesense.apiKey,
      connectionTimeoutSeconds: 2,
    });
  }

  async getStoreById(): Promise<any> {
    try {
      const currentLocation = window.location.hostname;
      const params = { q: '*', filter_by: `domain:=${currentLocation}`, per_page: 1 };
      const response = await this.client.collections('stores').documents().search(params);
      return response.hits?.[0]?.document || {};
    } catch (error) {
      console.error('Error fetching store:', error);
      return {};
    }
  }

  async getProductBySku(child_sku: string[]): Promise<Product[] | null> {
    try {
      const params = {
        q: '*',
        query_by: 'child_sku',
        filter_by: `store_id:=${this.storeId} && child_sku:=[${child_sku.join(',')}]`,
        include_fields: 'display_alias,name,thumbnail_image,variant_name,selling_price,child_sku,ean_number,tax,cgst,sgst,igst,cess,unit_price',
        page: 1,
        per_page: 1
      }
      let response: any = await this.client.collections('facility_products').documents().search(params);
      const products = this.productsResponseMapper(response);
      return products;
    } catch (error) {
      console.error('Error fetching product by SKU:', error);
      return null;
    }
  }

  async searchProductsDirectly(
    searchTerm: string,
  ): Promise<ProductSearchResult> {
    try {
      const searchParams = {
        q: searchTerm,
        query_by: 'ean_number,child_sku,name,display_alias',
        prefix: 'true,true,true,true',
        filter_by: `store_id:=${this.storeId}`,
        include_fields: 'display_alias,name,thumbnail_image,variant_name,selling_price,child_sku,ean_number,tax,cgst,sgst,igst,cess,unit_price',
        prioritize_exact_match: 'true'
      };
      let response: any = {};
      try {
        response = await this.client.collections('facility_products').documents().search(searchParams);
      } catch (error) {
        response = await this.client.collections('store_products').documents().search(searchParams);
      }
      const products = this.productsResponseMapper(response);
      return {
        products: products,
        totalProducts: response.found
      };
    } catch (error) {
      console.error('Error in searchProductsDirectly:', error);
      return {
        products: [],
        totalProducts: 0
      };
    }
  }

  productsResponseMapper(response: Record<string, any>): Product[] {
    return response['hits']?.map((hit: Record<string, any>) => hit['document']) || [];
  }
}