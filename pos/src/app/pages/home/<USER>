import {
  ChangeDetectorRef,
  Component,
  ViewChild,
  OnInit,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { IonContent } from '@ionic/angular/standalone';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HeaderComponent } from '../../components/header/header';
import { TypeSenseService } from '../../services/typesense';
import { Product, CartItem, ProductSearchResult } from '../../models';
import { TableComponent } from "src/app/components/table/table";
import { AutoCompleteModule } from 'primeng/autocomplete';
import { IftaLabelModule } from 'primeng/iftalabel';
import { TabsModule } from 'primeng/tabs';
import { BillingComponent } from 'src/app/components/billing/billing';
import { CartCalculationUtils } from 'src/app/utils/cart-calculation.utils';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  standalone: true,
  imports: [
    IonContent,
    CommonModule,
    FormsModule,
    BillingComponent,
    HeaderComponent,
    TableComponent,
    AutoCompleteModule,
    IftaLabelModule,
    TabsModule
  ],
})
export class HomePage implements OnInit, AfterViewInit {
  @ViewChild('searchInput') searchInput!: ElementRef;
  currentTab = 0;
  billingTabs = [
    {title: 'Billing', value: 0, items: [] as CartItem[]}
  ];
  searchSuggestions: Product[] = [];
  searchText: string = '';
  productsColumns: any[] = [
    { field: 'thumbnail_image', header: 'Image', type: 'image' },
    { field: 'name', header: 'Item Name' },
    { field: 'variant_name', header: 'Variant Name' },
    { field: 'child_sku', header: 'SKU' },
    { field: 'selling_price', header: 'Price', body: (item: any) => CartCalculationUtils.formatCurrency(item.selling_price) , class: 'text-orange-600 font-semibold' },
    { field: 'discount', header: 'Discount', body: (item: any) => CartCalculationUtils.formatCurrency(CartCalculationUtils.calculateItemDiscount(item)) },
    { field: 'total_amount', header: 'Total Amount', body: (item: any) => CartCalculationUtils.formatCurrency(CartCalculationUtils.calculateItemTotal(item)) },
    { field: 'quantity', header: 'Quantity', type: 'quantity' },
    { header: 'Actions', type: 'action', buttons: [{ icon: 'pi pi-trash', id: 'remove', label: 'Remove', outlined: true, severity: 'danger' }] },
  ];
  constructor(
    public typesenseService: TypeSenseService,
    private cdr: ChangeDetectorRef,
  ) { }

  ngOnInit(): void {
    this.searchSuggestions = [];
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.focusSearch();
    }, 100);
  }

  focusSearch() {
    if (this.searchInput?.nativeElement) {
      const inputElement = this.searchInput.nativeElement.querySelector('input');
      if (inputElement) {
        inputElement.focus();
        inputElement.setSelectionRange(
          inputElement.value.length,
          inputElement.value.length,
        );
      }
    }
  }

  onSearch(event: { query: string }) {
    const query = event.query;
    if (!query || query.trim() === '') {
      this.searchSuggestions = [];
      return;
    }
    this.typesenseService.searchProductsDirectly(query.trim()).then((result: ProductSearchResult) => {
        this.searchSuggestions = result.products || [];
      }).catch((error: any) => {
        console.error('Search error:', error);
        this.searchSuggestions = [];
      });
  }

  onEnterKey(event: any) {
    if (this.searchSuggestions?.length > 0) {
      event.preventDefault();
      event.stopPropagation();
      this.onSearchSelect({ value: this.searchSuggestions[0] });
    }
  }

  onSearchSelect(event: { value: Product } | Product) {
    // Handle both cases: when event is an object with value property or when it's the product itself
    const selectedProduct = 'value' in event ? event.value : event;
    if (selectedProduct) {
      this.addToCart(selectedProduct);
      this.searchSuggestions = [];
      this.searchText = '';
      setTimeout(() => {
        this.focusSearch();
      }, 100);
    }
  }

  onCartChange(_cartItems: CartItem[]) {
    this.refreshDisplayAndDetectChanges();
  }


  addToCart(product: Product) {
    const manualQuantity = product.quantity && product.quantity > 0 ? product.quantity : 1;
    this.updateCartItemQuantity(product, manualQuantity, true);
    product.quantity = 1;
    this.refreshDisplayAndDetectChanges();
  }

  private updateCartItemQuantity(product: Product | string, quantity: number, isAddition: boolean = false) {
    const productSku = typeof product === 'string' ? product : product.child_sku;
    const cartItem = this.billingTabs[this.currentTab].items.find(
      (item: any) => item.child_sku === productSku,
    );

    if (cartItem) {
      if (isAddition) {
        cartItem.quantity += quantity;
      } else {
        cartItem.quantity = quantity;
        if (cartItem.quantity <= 0) {
          this.removeFromCart({ child_sku: productSku });
          return;
        }
      }
    } else if (isAddition && typeof product === 'object') {
      const newCartItem = { ...product, quantity: quantity, tax: product.tax };
      this.billingTabs[this.currentTab].items.push(newCartItem);
    }
  }

  private refreshDisplayAndDetectChanges() {
    this.updateDisplayedProductsFromCart();
    this.cdr.detectChanges();
  }

  removeFromCart(product: { child_sku: string }) {
    if (!this.billingTabs[this.currentTab].items) {
      return;
    }

    const index = this.billingTabs[this.currentTab].items.findIndex(
      (item: any) => item.child_sku === product.child_sku,
    );

    if (index > -1) {
      this.billingTabs[this.currentTab].items.splice(index, 1);
      this.refreshDisplayAndDetectChanges();
    }
  }

  updateDisplayedProductsFromCart() {
    if (this.billingTabs[this.currentTab].items && this.billingTabs[this.currentTab].items.length > 0) {
      this.billingTabs[this.currentTab].items = [...this.billingTabs[this.currentTab].items].map(item => ({
        ...item,
        quantity: item.quantity
      }));
    } else {
      this.removeTab(this.currentTab);
    }
  }
  onChange(ev: any) {
    const { event, product, column } = ev;
    if (column.field === 'quantity') {
      product.quantity = event.value;
      this.updateCartItemQuantity(product, event.value, false);
      this.refreshDisplayAndDetectChanges();
    }
  }
  onActionClick(ev: any) {
    const { button, product } = ev;
    if (button.id === 'remove') {
      this.removeFromCart(product);
    }
  }

  onTabChange(ev: any){
    this.currentTab = ev;
  }
  removeTab(index: number) {
    if (this.billingTabs.length === 1) {
      this.billingTabs[0].items = [];
    } else {
      this.billingTabs.splice(index, 1);
    }
    this.currentTab = index === 0 ? 0 : this.billingTabs?.length === index ? index - 1 : index;
  }
  addTab() {
    this.billingTabs.push({title: 'Billing', value: this.billingTabs.length, items: []});
    this.currentTab = this.billingTabs.length - 1;
  }
}
