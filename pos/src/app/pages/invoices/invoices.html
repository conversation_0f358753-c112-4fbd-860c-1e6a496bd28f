<ion-content [fullscreen]="true">
   <app-header></app-header>
  <div class="grid grid-cols-3 gap-4 p-3">
    <div class="col-span-2 mt-3">
        <app-table
            [tableData]="invoices"
            [tableColumns]="invoicesColumns"
            [dataKey]="'order_id'"
            (onRowClick)="onRowClick($event)">
        </app-table>
    </div>
    <div class="col-span-1 p-3">
      <app-billing [noDataTitle]="''" [noDataMessage]="'Select Order to view details'" [showTable]="true" [cartItems]="cartItems || []"></app-billing>
      <!-- Print buttons for selected order -->
      <div class="mt-4 flex flex-col gap-2" *ngIf="selectedOrder">
        <h3 class="text-lg font-semibold mb-2">Print Options</h3>
        <button class="w-full" pButton severity="primary" [outlined]="true" label="Print Invoice Copy" (click)="printOrder()"></button>
      </div>
    </div>
</div>
</ion-content>
