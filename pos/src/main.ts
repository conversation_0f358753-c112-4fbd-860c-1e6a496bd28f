import { bootstrapApplication } from '@angular/platform-browser';
import { RouteReuseStrategy, provideRouter, withPreloading, PreloadAllModules } from '@angular/router';
import { IonicRouteStrategy, provideIonicAngular } from '@ionic/angular/standalone';
import { Animation, createAnimation } from '@ionic/angular/standalone';
import Aura from '@primeng/themes/aura';
import { providePrimeNG } from 'primeng/config';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { routes } from './app/app.routes';
import { AppComponent } from './app/app.component';
import { provideHttpClient, withFetch, withInterceptors } from '@angular/common/http';
import { responseInterceptor } from './app/interceptors/response.interceptor';
import { MessageService } from 'primeng/api';

// Empty animation function that returns a valid Animation object but does nothing
const emptyAnimation = (baseEl: HTMLElement, opts: any): Animation => {
  return createAnimation()
    .duration(0)
    .addElement(baseEl);
};

bootstrapApplication(AppComponent, {
  providers: [
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    provideIonicAngular({
      navAnimation: emptyAnimation // Only disable page transition animations
    }),
    provideRouter(routes, withPreloading(PreloadAllModules)),
    provideAnimationsAsync(),
    provideHttpClient(withFetch(), withInterceptors([responseInterceptor])),
    providePrimeNG({ theme: { preset: Aura, options: { darkModeSelector: '.app-dark' } } }),
    MessageService
  ],
});
