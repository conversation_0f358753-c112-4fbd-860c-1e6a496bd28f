# Print Functionality Documentation

This document describes the print functionality implemented in the POS system.

## Overview

The print functionality allows generating and printing receipts/invoices for orders and cart items. It includes:

- Print templates with tax calculations (CGST, SGST, IGST, CESS)
- Support for original and copy invoices
- Automatic tax calculation utilities
- Print service for easy integration

## Files Added/Modified

### New Files
- `src/app/models/print.model.ts` - Print-related interfaces
- `src/app/utils/print-template.util.ts` - HTML template generation
- `src/app/services/print.service.ts` - Print service
- `src/app/utils/print-template.util.spec.ts` - Unit tests

### Modified Files
- `src/app/models/index.ts` - Added print model export
- `src/app/utils/cart-calculation.utils.ts` - Added calculatePrintableTotals method
- `src/app/components/billing/billing.ts` - Added print functionality
- `src/app/components/billing/billing.html` - Added print buttons
- `src/app/pages/invoices/invoices.ts` - Added print functionality for orders
- `src/app/pages/invoices/invoices.html` - Added print buttons

## Usage

### 1. Print Cart Items

```typescript
import { PrintService } from '../services/print.service';

constructor(private printService: PrintService) {}

// Print current cart
printCart() {
  this.printService.printCart(
    this.cartItems,
    'ORDER-123',
    'Customer Name',
    'Cash',
    false // not a copy
  );
}

// Print cart copy
printCartCopy() {
  this.printService.printCart(
    this.cartItems,
    'ORDER-123',
    'Customer Name',
    'Cash',
    true // this is a copy
  );
}
```

### 2. Print Existing Order

```typescript
// Print order
printOrder(order: Order) {
  this.printService.printOrder(order, false);
}

// Print order copy
printOrderCopy(order: Order) {
  this.printService.printOrder(order, true);
}
```

### 3. Generate Template Only

```typescript
// Generate HTML template without printing
const htmlTemplate = this.printService.generateCartPrintTemplate(
  cartItems,
  'ORDER-123',
  'Customer Name',
  'Cash',
  false
);

// Use the template as needed
console.log(htmlTemplate);
```

## Features

### Tax Calculations
- Automatic CGST/SGST calculation for intra-state transactions
- IGST calculation for inter-state transactions
- CESS calculation when applicable
- Tax amounts displayed per item and in totals

### Receipt Format
- 80mm thermal printer compatible
- Company header with GSTIN
- Order details (ID, date, customer)
- Itemized list with tax breakdown
- Summary totals
- Payment method
- Footer with thank you message

### Copy Invoices
- Watermark overlay for invoice copies
- "(COPY)" suffix in title
- Same format as original with visual distinction

## Models

### PrintableItem
```typescript
interface PrintableItem {
  sku: string;
  name?: string;
  quantity: number;
  unit_price: number;  // MRP
  sale_price: number;  // Actual selling price
  mrp: number;         // Maximum Retail Price
  discount: number;    // Calculated discount
  total: number;
  // Tax fields
  cgst?: number;       // CGST percentage
  sgst?: number;       // SGST percentage
  igst?: number;       // IGST percentage
  cess?: number;       // CESS amount
  // Calculated tax amounts
  cgstAmount?: number;
  sgstAmount?: number;
  igstAmount?: number;
  cessAmount?: number;
  totalTax?: number;
  totalWithTax?: number;
}
```

### PrintTotals
```typescript
interface PrintTotals {
  totalTaxableAmount: number;
  totalCgstAmount: number;
  totalSgstAmount: number;
  totalIgstAmount: number;
  totalGstAmount: number;
  totalCessAmount: number;
  totalQuantity: number;
}
```

## Integration Points

### Billing Component
- Print buttons added to billing interface
- Automatic printing after successful order creation
- Manual print options for current cart

### Invoices Page
- Print buttons for selected orders
- Support for printing original and copy invoices
- Integration with order details view

## Configuration

Store details can be configured in the print service:
- Store address
- Phone number
- GSTIN number
- Company name

## Testing

Run the print template tests:
```bash
ng test --include="**/print-template.util.spec.ts"
```

## Browser Compatibility

The print functionality uses `window.open()` and `window.print()` APIs, which are supported in all modern browsers. The generated HTML uses Tailwind CSS for styling and is optimized for thermal printers.
